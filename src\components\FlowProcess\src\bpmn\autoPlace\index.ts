// /**
//  * A palette that allows you to create BPMN _and_ custom elements.
//  */
// import AutoPlace from 'bpmn-js/lib/features/auto-place/BpmnAutoPlace.js';
// import { getYmNewShapePosition } from '../autoPlaceUtil';
// function YmAutoPlace(eventBus: any) {
//   eventBus.on('autoPlace', function (context: any) {
//     var shape = context.shape,
//       source = context.source;
//     return getYmNewShapePosition(source, shape);
//   });
// }
//
// YmAutoPlace.$inject = ['eventBus'];
//
// export default YmAutoPlace;
