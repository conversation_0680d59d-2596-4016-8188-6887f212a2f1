<template>
  <a-form-item label="模型ID">
    <a-input v-model:value="activeData.modelId" placeholder="请输入模型ID" />
  </a-form-item>
  <a-form-item>
    <template #label>
      说明
      <BasicHelp text="模型ID用于标识数据表，用于获取对应的修改记录" />
    </template>
    <div class="text-gray-500 text-sm">
      <p>• 模型ID是数据表的唯一标识</p>
      <p>• 用于获取该表单数据的修改历史记录</p>
      <p>• 如果不填写，将无法显示修改记录</p>
    </div>
  </a-form-item>
</template>

<script lang="ts" setup>
  import { BasicHelp } from '@/components/Basic';

  defineProps(['activeData']);
</script>
