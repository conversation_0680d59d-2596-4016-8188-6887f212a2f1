<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="高级列属性"
    @ok="handleSubmit"
    destroyOnClose
    class="advanced-column-modal"
  >
    <div class="advanced-column-modal-body">
      <a-form :colon="false" labelAlign="left" :labelCol="{ style: { width: '90px' } }" class="right-board-form" ref="formRef">
      <a-form-item label="高级渲染">
        <jnpf-select v-model:value="formData.advancedRender" :options="advancedRenderOptions" allowClear />
      </a-form-item>

      <!-- 进度型参数设置 -->
      <template v-if="formData.advancedRender === 'progress'">
        <a-form-item label="显示形状">
          <a-radio-group v-model:value="formData.progressShape" button-style="solid">
            <a-radio-button value="line">线条</a-radio-button>
            <a-radio-button value="circle">圆形</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="显示百分比">
          <a-switch v-model:checked="formData.progressShowInfo" />
        </a-form-item>
        <!-- 大小输入框 -->
        <a-form-item label="显示大小">
          <a-input-number v-model:value="formData.progressSize" />
        </a-form-item>
      </template>

      <!-- 条件格式化 -->
      <template v-if="['status', 'tag'].includes(formData.advancedRender)">
      <a-form-item label="条件格式化">
        <a-switch v-model:checked="formData.enableConditionFormat" />
      </a-form-item>
      <template v-if="formData.enableConditionFormat">
        <a-divider>条件格式化规则</a-divider>
        <div class="condition-rules">
          <div v-for="(rule, index) in formData.conditionRules" :key="index" class="condition-rule-item">
            <div class="rule-header">
              <span>规则 {{ index + 1 }}</span>
              <a-button type="link" danger @click="removeRule(index)" size="small">删除</a-button>
            </div>
            <a-form-item label="条件值">
              <a-input v-model:value="rule.value" placeholder="请输入条件值" />
            </a-form-item>
            <a-form-item label="文本颜色">
              <JnpfColorPicker v-model:value="rule.textColor" :show-alpha="false"/>
            </a-form-item>
            <a-form-item label="图标">
              <JnpfIconPicker v-model:value="rule.icon" />
            </a-form-item>
          </div>
          <a-button type="dashed" block @click="addRule">
            <plus-outlined /> 添加规则
          </a-button>
        </div>
      </template>
      </template>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { JnpfIconPicker } from '@/components/Jnpf/IconPicker';
  import { JnpfColorPicker } from '@/components/Jnpf/ColorPicker';

  const advancedRenderOptions = [
    { id: 'goDetail', fullName: '详情链接' },
    { id: 'goUpdate', fullName: '更新链接' },
    { id: 'status', fullName: '状态' },
    { id: 'tag', fullName: '标签' },
    { id: 'progress', fullName: '进度' },
  ];

  const formRef = ref(null);
  const formData :any = reactive({
    advancedRender: '',
    enableConditionFormat: false,
    conditionRules: [],
    progressShape: 'line',  // 默认线形
    progressShowInfo: true, // 默认显示数字
    progressSize: 30, // 默认大小
  });

  const [registerModal, { closeModal }] = useModalInner((data) => {
    // 初始化表单数据
    Object.keys(formData).forEach(key => {
      formData[key] = data[key];
    });

    // 确保conditionRules是数组
    if (!Array.isArray(formData.conditionRules)) {
      formData.conditionRules = [];
    }
  });

  function addRule() {
    formData.conditionRules.push({
      value: '',
      textColor: '',
      icon: ''
    });
  }

  function removeRule(index) {
    formData.conditionRules.splice(index, 1);
  }

  function handleSubmit() {
    const result = {
      ...formData,
    };

    // 如果没有启用条件格式化，清空规则
    if (!result.enableConditionFormat) {
      result.conditionRules = [];
    }

    closeModal();
    emit('confirm', result);
  }

  const emit = defineEmits(['confirm']);
</script>

<style lang="less" scoped>
  .advanced-column-modal {
    .advanced-column-modal-body {
      min-height: 150px;
      padding-bottom: 20px;
    }

    .condition-rules {
      margin-bottom: 16px;
    }

    .condition-rule-item {
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 12px;
    }

    .rule-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .color-preview {
      width: 16px;
      height: 16px;
      border: 1px solid #d9d9d9;
    }

    .color-picker-container {
      display: flex;
      align-items: center;

      .ant-input-group {
        flex: 1;
        margin-right: 8px;
      }

      .jnpf-color-picker {
        flex-shrink: 0;
      }
    }
  }
</style>
